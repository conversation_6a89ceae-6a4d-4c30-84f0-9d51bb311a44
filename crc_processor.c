#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// CRC-8多项式 (x^8 + x^2 + x^1 + 1)
#define CRC8_POLYNOMIAL 0x07

// 函数声明
unsigned char calculate_crc8(int data_array[1999], int array_size);
int read_csv_data(char filename[], int data_array[1999]);
int write_csv_data(char filename[], unsigned char crc_value, int data_array[1999]);

int main() {
    int data_array[1999];  // 存储从CSV读取的1999个数据
    unsigned char crc_result;
    int read_count;
    
    printf("开始处理CSV文件...\n");
    
    // 从init.csv读取B列第2-2000行数据
    read_count = read_csv_data("init.csv", data_array);
    if (read_count <= 0) {
        printf("错误：无法读取CSV文件或数据为空\n");
        return 1;
    }
    
    printf("成功读取 %d 个数据\n", read_count);
    
    // 计算CRC-8校验码
    crc_result = calculate_crc8(data_array, read_count);
    printf("CRC-8校验码: 0x%02X (%d)\n", crc_result, crc_result);
    
    // 写入新的CSV文件
    if (write_csv_data("init_CRC.csv", crc_result, data_array) == 0) {
        printf("成功生成 init_CRC.csv 文件\n");
    } else {
        printf("错误：无法写入CSV文件\n");
        return 1;
    }
    
    printf("处理完成！\n");
    return 0;
}

// CRC-8计算函数
unsigned char calculate_crc8(int data_array[1999], int array_size) {
    unsigned char crc = 0x00;  // 初始值
    int i, j;
    
    // 对每个32位整数进行CRC计算
    for (i = 0; i < array_size; i++) {
        // 将32位整数分解为4个字节进行处理
        unsigned char bytes[4];
        bytes[0] = (data_array[i] >> 24) & 0xFF;  // 最高字节
        bytes[1] = (data_array[i] >> 16) & 0xFF;
        bytes[2] = (data_array[i] >> 8) & 0xFF;
        bytes[3] = data_array[i] & 0xFF;          // 最低字节
        
        // 对每个字节计算CRC
        for (j = 0; j < 4; j++) {
            crc ^= bytes[j];
            
            // 进行8次位移和异或运算
            int bit;
            for (bit = 0; bit < 8; bit++) {
                if (crc & 0x80) {
                    crc = (crc << 1) ^ CRC8_POLYNOMIAL;
                } else {
                    crc = crc << 1;
                }
            }
        }
    }
    
    return crc;
}

// 从CSV文件读取B列数据
int read_csv_data(char filename[], int data_array[1999]) {
    FILE *file;
    char line[1000];
    int row = 0;
    int data_count = 0;
    
    file = fopen(filename, "r");
    if (file == NULL) {
        printf("错误：无法打开文件 %s\n", filename);
        return -1;
    }
    
    // 逐行读取文件
    while (fgets(line, sizeof(line), file) != NULL && data_count < 1999) {
        row++;
        
        // 跳过第一行，从第2行开始读取
        if (row < 2) {
            continue;
        }
        
        // 如果超过第2000行，停止读取
        if (row > 2000) {
            break;
        }
        
        // 解析CSV行，提取B列数据
        char *token;
        int column = 0;
        char line_copy[1000];
        strcpy(line_copy, line);
        
        token = strtok(line_copy, ",");
        while (token != NULL) {
            column++;
            if (column == 2) {  // B列是第2列
                // 将字符串转换为整数
                int value = atoi(token);
                data_array[data_count] = value;
                data_count++;
                break;
            }
            token = strtok(NULL, ",");
        }
    }
    
    fclose(file);
    return data_count;
}

// 写入新的CSV文件
int write_csv_data(char filename[], unsigned char crc_value, int data_array[1999]) {
    FILE *file;
    int i;
    
    file = fopen(filename, "w");
    if (file == NULL) {
        printf("错误：无法创建文件 %s\n", filename);
        return -1;
    }
    
    // 写入CRC校验码作为第一行
    fprintf(file, "%d\n", crc_value);
    
    // 写入1999个数据
    for (i = 0; i < 1999; i++) {
        fprintf(file, "%d\n", data_array[i]);
    }
    
    fclose(file);
    return 0;
}
