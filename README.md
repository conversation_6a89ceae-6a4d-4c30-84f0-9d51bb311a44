# CRC-8 CSV处理程序

## 程序功能
这个C语言程序用于：
1. 从`init.csv`文件中读取B列第2-2000行的数据（共1999个数据）
2. 将这些数据转换为32位整数数组
3. 使用CRC-8算法对整个数组进行校验
4. 生成包含CRC校验码和原数据的新CSV文件`init_CRC.csv`

## 文件说明
- `crc_processor.c` - 主程序源代码
- `Makefile` - 编译配置文件
- `init.csv` - 输入数据文件
- `init_CRC.csv` - 输出文件（程序运行后生成）

## 编译和运行

### 方法1：使用Makefile
```bash
make
make run
```

### 方法2：直接编译
```bash
gcc -Wall -std=c99 -o crc_processor crc_processor.c
./crc_processor.exe
```

## 程序特点
- **简单易懂**：代码结构清晰，注释详细
- **不使用指针**：完全使用数组操作，避免指针复杂性
- **容易修改**：模块化设计，各功能独立
- **错误处理**：包含文件操作和数据读取的错误检查

## 输出文件格式
`init_CRC.csv`文件包含2000行数据：
- 第1行：8位CRC校验码（十进制格式）
- 第2-2000行：原始数据（1999个32位整数）

## CRC-8算法
- 使用多项式：x^8 + x^2 + x^1 + 1 (0x07)
- 初始值：0x00
- 对每个32位整数的4个字节分别进行CRC计算

## 修改说明
如需修改程序，主要可调整的部分：
1. `CRC8_POLYNOMIAL` - 修改CRC多项式
2. `read_csv_data()` - 修改数据读取逻辑
3. `calculate_crc8()` - 修改CRC计算方法
4. `write_csv_data()` - 修改输出格式
